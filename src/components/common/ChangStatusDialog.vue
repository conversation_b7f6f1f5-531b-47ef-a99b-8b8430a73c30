<template>
  <q-dialog v-model="dialog" persistent>
    <q-card class="q-pa-lg q-mx-sm" style="border-radius: 16px; width: 100%; max-width: 400px">
      <q-card-section class="text-h6 text-center">เปลี่ยนสถานะการเผยแพร่</q-card-section>

      <!-- Card สถานะ 2 ฝั่ง -->
      <q-card-section>
        <div :class="$q.screen.lt.sm ? 'column q-gutter-sm' : 'row justify-around'">
          <!-- FALSE -->
          <div :class="$q.screen.lt.sm ? '' : 'col-5'">
            <q-card
              flat
              bordered
              class="text-center cursor-pointer"
              :class="!assStastatus ? 'bg-secondary text-white' : ''"
              @click="assStastatus = false"
            >
              <q-card-section>
                <q-icon name="cancel" size="24px" />
                <div class="text-subtitle1 q-mt-sm">ไม่เผยแพร่</div>
              </q-card-section>
            </q-card>
          </div>

          <!-- TRUE -->
          <div :class="$q.screen.lt.sm ? '' : 'col-5'">
            <q-card
              flat
              bordered
              class="text-center cursor-pointer"
              :class="assStastatus ? 'bg-positive text-white' : ''"
              @click="assStastatus = true"
            >
              <q-card-section>
                <q-icon name="check_circle" size="24px" />
                <div class="text-subtitle1 q-mt-sm">เผยแพร่อยู่</div>
              </q-card-section>
            </q-card>
          </div>
        </div>
      </q-card-section>

      <!-- ปุ่ม -->
      <q-card-actions
        :class="$q.screen.lt.sm ? 'column items-stretch' : 'row justify-end'"
        class="q-gutter-sm"
      >
        <q-btn
          label="ยกเลิก"
          color="grey-4"
          class="text-black"
          @click="dialog = false"
          :class="$q.screen.lt.sm ? 'full-width' : ''"
        />
        <q-btn
          label="ยืนยัน"
          color="primary"
          class="text-black"
          @click="confirm"
          :class="$q.screen.lt.sm ? 'full-width' : ''"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { useQuasar } from 'quasar';
import { AssessmentService } from 'src/services/asm/assessmentService';
import type { AssessmentType } from 'src/types/data';

const $q = useQuasar();

const dialog = ref(false);
const assId = ref(0);
const assStastatus = ref(false);
const confirmLabel = ref('ยืนยัน');
const path = ref<AssessmentType>('evaluate');

function openDialog(id: number, status: boolean, okStr: string, pathPrefix: string) {
  assId.value = id;
  assStastatus.value = status;
  confirmLabel.value = okStr;
  path.value = pathPrefix as AssessmentType;
  dialog.value = true;
}

async function confirm() {
  await new AssessmentService(path.value).updateOne(assId.value, { status: assStastatus.value });
  dialog.value = false;
  window.location.reload();
}

defineExpose({ openDialog });
</script>
